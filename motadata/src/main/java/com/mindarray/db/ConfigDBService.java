/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.db;

import com.mindarray.util.MotadataConfigUtil;
import io.vertx.codegen.annotations.Fluent;
import io.vertx.codegen.annotations.GenIgnore;
import io.vertx.codegen.annotations.ProxyGen;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

@ProxyGen
public interface ConfigDBService
{

    @GenIgnore
    static void create(Vertx vertx, Handler<AsyncResult
            <ConfigDBService>> handler)
    {
        new ConfigDBServiceImpl(vertx, handler);
    }

    @GenIgnore
    static ConfigDBService createProxy(Vertx vertx, String address)
    {
        return new ConfigDBServiceVertxEBProxy(vertx, address, new DeliveryOptions().setSendTimeout(MotadataConfigUtil.getConfigDBServiceTimeoutMillis()));
    }

    @Fluent
    ConfigDBService save(String collection, JsonObject document, String user, String remoteIP,
                         Handler<AsyncResult<Long>> handler);

    @Fluent
    ConfigDBService saveAll(String collection, JsonArray documents, String user, String remoteIP,
                            Handler<AsyncResult<JsonArray>> handler);

    @Fluent
    ConfigDBService get(String collection, JsonObject query,
                        Handler<AsyncResult<JsonArray>> handler);

    @Fluent
    ConfigDBService getById(String collection, long id,
                            Handler<AsyncResult<JsonObject>> handler);

    @Fluent
    ConfigDBService getOne(String collection,
                           Handler<AsyncResult<JsonObject>> handler);

    @Fluent
    ConfigDBService getOneByQuery(String collection, JsonObject query,
                                  Handler<AsyncResult<JsonObject>> handler);

    @Fluent
    ConfigDBService getAll(String collection,
                           Handler<AsyncResult<JsonArray>> handler);


    @Fluent
    ConfigDBService delete(String collection, JsonObject query, String user, String remoteIP,
                           Handler<AsyncResult<JsonArray>> handler);

    @Fluent
    ConfigDBService deleteAll(String collection, JsonObject query, String user, String remoteIP,
                              Handler<AsyncResult<JsonArray>> handler);

    @Fluent
    ConfigDBService drop(String collection, Handler<AsyncResult<Void>> handler);

    @Fluent
    ConfigDBService update(String collection, JsonObject query, JsonObject document, String user, String remoteIP,
                           Handler<AsyncResult<JsonArray>> handler);

    @Fluent
    ConfigDBService upsert(String collection, JsonObject document, String user, String remoteIP,
                           Handler<AsyncResult<Long>> handler);

    @Fluent
    ConfigDBService upsertAll(String collection, JsonArray document, String user, String remoteIP,
                           Handler<AsyncResult<JsonArray>> handler);

    @Fluent
    ConfigDBService updateAll(String collection, JsonObject query, JsonObject document, String user, String remoteIP,
                              Handler<AsyncResult<JsonArray>> handler);


    @Fluent
    ConfigDBService compact();

    void backup(JsonObject context, Handler<AsyncResult<JsonObject>> handler);

    void close(Handler<AsyncResult<Void>> handler);

    /*
    ----------------------------- Compliance Related methods -----------------------------
     */

    @Fluent
    ConfigDBService insert(String table, JsonObject document, String user, String remoteIP, Handler<AsyncResult<Long>> handler);

    @Fluent
    ConfigDBService execute(String table, String document, String user, String remoteIP, Handler<AsyncResult<Long>> handler);

    @Fluent
    ConfigDBService insertAll(String table, JsonArray document, String user, String remoteIP, Handler<AsyncResult<Long>> handler);

    @Fluent
    ConfigDBService select(String table, JsonObject query, Handler<AsyncResult<JsonObject>> handler);

    @Fluent
    ConfigDBService deleteRow(String table, JsonObject query, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler);
}
